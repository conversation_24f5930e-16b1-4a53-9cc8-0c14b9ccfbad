import React, { useState, useEffect } from 'react';
import { X, CreditCard, Shield, Clock, AlertCircle, Lock, CheckCircle, Truck, Award, Star } from 'lucide-react';
import Button from './ui/Button';
import Input from './ui/Input';
import Card from './ui/Card';
import Badge from './ui/Badge';
import paymentService from '../services/paymentService';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: {
    _id: string;
    title: string;
    price: number;
    seller: {
      displayName: string;
    };
  };
  onPaymentSuccess: (orderId: string) => void;
  onPaymentError: (error: string) => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  project,
  onPaymentSuccess,
  onPaymentError
}) => {
  const [customerPhone, setCustomerPhone] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentOrder, setCurrentOrder] = useState<any>(null);
  const [paymentStatus, setPaymentStatus] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [existingPayment, setExistingPayment] = useState<any>(null);
  const [showExistingPaymentDialog, setShowExistingPaymentDialog] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setCustomerPhone('');
      setIsProcessing(false);
      setCurrentOrder(null);
      setPaymentStatus('');
      setError('');
      setExistingPayment(null);
      setShowExistingPaymentDialog(false);
    }
  }, [isOpen]);

  // Handle payment initiation
  const handlePayment = async () => {
    try {
      setIsProcessing(true);
      setError('');

      // Validate phone number if provided (more lenient validation)
      if (customerPhone && customerPhone.trim() !== '') {
        // Remove any non-digit characters
        const cleanPhone = customerPhone.replace(/\D/g, '');

        // Check if it's a valid 10-digit Indian mobile number
        if (!/^[6-9]\d{9}$/.test(cleanPhone)) {
          throw new Error('Please enter a valid 10-digit mobile number starting with 6, 7, 8, or 9');
        }

        // Update the phone number to the cleaned version
        setCustomerPhone(cleanPhone);
      }

      // Create payment order
      const orderResponse = await paymentService.createOrder(project._id, customerPhone, false, null);

      // Handle existing payment scenario
      if (!orderResponse.success && orderResponse.isExistingPayment) {
        console.log('📋 Existing payment found, showing dialog');
        setExistingPayment(orderResponse.data);
        setShowExistingPaymentDialog(true);
        setIsProcessing(false);
        return;
      }

      if (!orderResponse.success) {
        throw new Error(orderResponse.message || 'Failed to create payment order');
      }

      const orderData = orderResponse.data;
      setCurrentOrder(orderData);
      setPaymentStatus('ACTIVE');

      // Initiate Razorpay payment
      await paymentService.initiateRazorpayPayment({
        razorpayOrderId: orderData.razorpayOrderId,
        razorpayKeyId: orderData.razorpayKeyId,
        amount: orderData.amount,
        currency: orderData.currency,
        customerDetails: orderData.customerDetails
      });

      // The payment will redirect to success/failure page
      // We'll handle the verification there

    } catch (error: any) {
      console.error('Payment error:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Payment failed';
      setError(errorMessage);
      onPaymentError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle existing payment - resume payment
  const handleResumeExistingPayment = async () => {
    if (!existingPayment) return;

    try {
      setIsProcessing(true);
      setShowExistingPaymentDialog(false);

      // Set the existing payment as current order
      setCurrentOrder({
        orderId: existingPayment.orderId,
        razorpayOrderId: existingPayment.razorpayOrderId,
        amount: project.price,
        currency: 'INR'
      });
      setPaymentStatus(existingPayment.status);

      // Initiate Razorpay payment with existing order
      await paymentService.initiateRazorpayPayment({
        razorpayOrderId: existingPayment.razorpayOrderId,
        razorpayKeyId: import.meta.env.VITE_RAZORPAY_KEY_ID,
        amount: project.price,
        currency: 'INR',
        customerDetails: {
          customerName: existingPayment.customerName,
          customerEmail: existingPayment.customerEmail,
          customerPhone: existingPayment.customerPhone
        }
      });

    } catch (error: any) {
      console.error('Error resuming payment:', error);
      setError(error.message || 'Failed to resume payment');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle existing payment - cancel and create new
  const handleCancelAndCreateNew = async () => {
    if (!existingPayment) return;

    try {
      setIsProcessing(true);
      setShowExistingPaymentDialog(false);

      // Cancel existing payment
      await paymentService.cancelOrder(existingPayment.orderId);
      console.log('✅ Existing payment cancelled');

      // Create new payment order
      const orderResponse = await paymentService.createOrder(project._id, customerPhone);

      if (!orderResponse.success) {
        throw new Error(orderResponse.message || 'Failed to create payment order');
      }

      const orderData = orderResponse.data;
      setCurrentOrder(orderData);
      setPaymentStatus('ACTIVE');

      // Initiate Razorpay payment
      await paymentService.initiateRazorpayPayment({
        razorpayOrderId: orderData.razorpayOrderId,
        razorpayKeyId: orderData.razorpayKeyId,
        amount: orderData.amount,
        currency: orderData.currency,
        customerDetails: orderData.customerDetails
      });

    } catch (error: any) {
      console.error('Error creating new payment:', error);
      setError(error.message || 'Failed to create new payment');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle order status check
  const checkOrderStatus = async () => {
    if (!currentOrder) return;

    try {
      const statusResponse = await paymentService.getOrderStatus(currentOrder.orderId);
      if (statusResponse.success) {
        const newStatus = statusResponse.data.status;
        setPaymentStatus(newStatus);

        if (paymentService.isPaymentSuccessful(newStatus)) {
          onPaymentSuccess(currentOrder.orderId);
          onClose();
        } else if (paymentService.isPaymentFailed(newStatus)) {
          setError('Payment failed. Please try again.');
        }
      }
    } catch (error: any) {
      console.error('Error checking order status:', error);
    }
  };

  if (!isOpen) return null;

  // Existing Payment Dialog
  if (showExistingPaymentDialog && existingPayment) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="w-full max-w-md bg-white dark:bg-gray-900 rounded-2xl shadow-2xl p-6">
          <div className="text-center mb-6">
            <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" width="48" height="48" />
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Existing Payment Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              You already have a pending payment for this project.
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Order ID:</span>
                <span className="font-mono text-gray-900 dark:text-white">
                  {existingPayment.orderId}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Status:</span>
                <Badge variant={paymentService.getStatusColor(existingPayment.status) as any}>
                  {paymentService.getStatusText(existingPayment.status)}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Created:</span>
                <span className="text-gray-900 dark:text-white">
                  {new Date(existingPayment.createdAt).toLocaleString()}
                </span>
              </div>
              {existingPayment.expiryTime && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Expires:</span>
                  <span className="text-gray-900 dark:text-white">
                    {new Date(existingPayment.expiryTime).toLocaleString()}
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <Button
              variant="primary"
              size="lg"
              onClick={handleResumeExistingPayment}
              disabled={isProcessing}
              className="w-full"
              leftIcon={<CreditCard className="h-4 w-4" width="16" height="16" />}
            >
              {isProcessing ? 'Processing...' : 'Resume Payment'}
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={handleCancelAndCreateNew}
              disabled={isProcessing}
              className="w-full"
            >
              Cancel & Create New
            </Button>

            <Button
              variant="ghost"
              size="lg"
              onClick={() => setShowExistingPaymentDialog(false)}
              disabled={isProcessing}
              className="w-full"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="w-full max-w-4xl bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Lock className="h-6 w-6 text-white" width="24" height="24" />
              <h2 className="text-xl font-bold text-white">Secure Checkout</h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
              disabled={isProcessing}
            >
              <X className="h-5 w-5" width="20" height="20" />
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
          {/* Left Column - Order Summary */}
          <div className="lg:col-span-2 p-6 border-r border-gray-200 dark:border-gray-700">
            <div className="space-y-6">

              {/* Order Summary */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Order Summary
                </h3>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                      <CreditCard className="h-8 w-8 text-gray-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {project.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        by {project.seller?.displayName || 'Unknown Seller'}
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="default" className="capitalize bg-orange-100 text-orange-800">
                          {project.category}
                        </Badge>
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          Digital Download
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">
                        {paymentService.formatCurrency(project.price)}
                      </div>
                      <div className="text-sm text-gray-500 line-through">
                        ₹{(project.price * 1.3).toFixed(0)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Delivery Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Delivery Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <Truck className="h-5 w-5 text-green-600 mr-3" />
                    <div>
                      <div className="font-medium text-green-800 dark:text-green-400">
                        Instant Digital Delivery
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-500">
                        Access immediately after payment
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <Shield className="h-5 w-5 text-blue-600 mr-3" width="20" height="20" />
                    <div>
                      <div className="font-medium text-blue-800 dark:text-blue-400">
                        Secure Payment
                      </div>
                      <div className="text-sm text-blue-600 dark:text-blue-500">
                        Protected by Razorpay encryption
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <Award className="h-5 w-5 text-purple-600 mr-3" />
                    <div>
                      <div className="font-medium text-purple-800 dark:text-purple-400">
                        Money-back Guarantee
                      </div>
                      <div className="text-sm text-purple-600 dark:text-purple-500">
                        30-day satisfaction guarantee
                      </div>
                    </div>
                  </div>
                </div>
              </div>

          {/* Current Order Status */}
          {currentOrder && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Order Status:</span>
                <Badge variant={paymentService.getStatusColor(paymentStatus) as any}>
                  {paymentService.getStatusText(paymentStatus)}
                </Badge>
              </div>
              <p className="text-xs text-gray-600">Order ID: {currentOrder.orderId}</p>
              {paymentStatus === 'ACTIVE' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={checkOrderStatus}
                  className="mt-2 w-full"
                >
                  Check Status
                </Button>
              )}
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {/* Payment Form */}
          {!currentOrder && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mobile Number (Optional)
                </label>
                <Input
                  type="tel"
                  placeholder="Enter 10-digit mobile number"
                  value={customerPhone}
                  onChange={(e) => setCustomerPhone(e.target.value)}
                  maxLength={10}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  For payment notifications and order updates
                </p>
              </div>

              {/* Security Info */}
              <div className="mb-6 p-3 bg-green-50 rounded-lg">
                <div className="flex items-center mb-2">
                  <Shield className="h-4 w-4 text-green-600 mr-2" width="16" height="16" />
                  <span className="text-sm font-medium text-green-800">Secure Payment</span>
                </div>
                <p className="text-xs text-green-700">
                  Your payment is secured by Razorpay with 256-bit SSL encryption
                </p>
              </div>

              {/* Payment Button */}
              <Button
                variant="primary"
                size="lg"
                onClick={handlePayment}
                disabled={isProcessing}
                className="w-full"
                leftIcon={<CreditCard className="h-4 w-4" width="16" height="16" />}
              >
                {isProcessing ? 'Processing...' : `Pay ${paymentService.formatCurrency(project.price)}`}
              </Button>
            </>
          )}

            </div>
          </div>

          {/* Right Column - Payment Sidebar */}
          <div className="lg:col-span-1 bg-gray-50 dark:bg-gray-800 p-6">
            <div className="sticky top-0 space-y-6">
              {/* Price Breakdown */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Payment Details
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Item price:</span>
                    <span className="text-gray-900 dark:text-white">
                      ₹{(project.price * 1.3).toFixed(0)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Discount (23%):</span>
                    <span className="text-green-600">
                      -₹{((project.price * 1.3) - project.price).toFixed(0)}
                    </span>
                  </div>
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                    <div className="flex justify-between">
                      <span className="text-lg font-semibold text-gray-900 dark:text-white">
                        Total:
                      </span>
                      <span className="text-lg font-bold text-orange-600">
                        {paymentService.formatCurrency(project.price)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Form */}
              {!currentOrder && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Mobile Number (Optional)
                    </label>
                    <Input
                      type="tel"
                      placeholder="Enter 10-digit mobile number"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                      maxLength={10}
                      className="w-full"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      For payment notifications and order updates
                    </p>
                  </div>

                  {/* Payment Button */}
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handlePayment}
                    disabled={isProcessing}
                    className="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-4"
                    leftIcon={<CreditCard className="h-5 w-5" />}
                  >
                    {isProcessing ? 'Processing...' : `Pay ${paymentService.formatCurrency(project.price)}`}
                  </Button>

                  {/* Payment Methods */}
                  <div className="text-center">
                    <p className="text-xs text-gray-500 mb-2">
                      Secure payment powered by Razorpay
                    </p>
                    <div className="flex justify-center space-x-2 text-xs text-gray-400">
                      <span>UPI</span>
                      <span>•</span>
                      <span>Cards</span>
                      <span>•</span>
                      <span>Net Banking</span>
                      <span>•</span>
                      <span>Wallets</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Order Status */}
              {currentOrder && (
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Order Status:
                      </span>
                      <Badge variant={paymentService.getStatusColor(paymentStatus) as any}>
                        {paymentService.getStatusText(paymentStatus)}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Order ID: {currentOrder.orderId}
                    </p>
                    {paymentStatus === 'ACTIVE' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={checkOrderStatus}
                        className="mt-3 w-full"
                      >
                        Check Status
                      </Button>
                    )}
                  </div>

                  {/* Expiry Warning */}
                  {paymentStatus === 'ACTIVE' && (
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-yellow-600 mr-2" />
                        <p className="text-sm text-yellow-800 dark:text-yellow-400">
                          Payment session expires in 30 minutes
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Trust Indicators */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    <span>SSL secured checkout</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    <span>Instant access after payment</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    <span>24/7 customer support</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mx-6 mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentModal;
