// Test script to verify discount functionality with Razorpay integration
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { 
  initializeRazorpay, 
  createPaymentOrder, 
  getOrderStatus,
  generateOrderId,
  generateCustomerId,
  formatAmount,
  validatePaymentAmount
} from './config/razorpay.js';

// Load environment variables
dotenv.config();

// Test data
const testProject = {
  _id: 'test_project_123',
  title: 'Test Project with Discount',
  price: 100, // ₹100
  seller: 'test_seller_123'
};

const testUser = {
  _id: 'test_user_123',
  email: '<EMAIL>',
  displayName: 'Test User'
};

const testDiscountCode = {
  code: 'NEGO-TEST123',
  discountAmount: 20, // ₹20 discount
  originalPrice: 100,
  finalPrice: 80 // ₹80 after discount
};

async function testDiscountPaymentFlow() {
  console.log('🧪 Testing Discount Payment Flow with Razorpay...\n');

  try {
    // 1. Test Razorpay initialization
    console.log('1. Testing Razorpay initialization...');
    const razorpayInitialized = initializeRazorpay();
    if (!razorpayInitialized) {
      throw new Error('Failed to initialize Razorpay');
    }
    console.log('   Result: ✅ Razorpay initialized successfully\n');

    // 2. Test discount calculation
    console.log('2. Testing discount calculation...');
    const originalPrice = testProject.price;
    const finalPrice = testDiscountCode.finalPrice;
    const discountAmount = originalPrice - finalPrice;
    const discountPercentage = Math.round((discountAmount / originalPrice) * 100);
    
    console.log(`   Original Price: ₹${originalPrice}`);
    console.log(`   Discount Amount: ₹${discountAmount}`);
    console.log(`   Final Price: ₹${finalPrice}`);
    console.log(`   Discount Percentage: ${discountPercentage}%`);
    console.log('   Result: ✅ Discount calculation correct\n');

    // 3. Test payment amount formatting and validation
    console.log('3. Testing payment amount formatting...');
    const formattedAmount = formatAmount(finalPrice);
    console.log(`   Formatted Amount: ${formattedAmount} (should be ${finalPrice})`);
    
    try {
      validatePaymentAmount(formattedAmount);
      console.log('   Result: ✅ Payment amount validation passed\n');
    } catch (error) {
      throw new Error(`Payment amount validation failed: ${error.message}`);
    }

    // 4. Test order creation with discount
    console.log('4. Testing Razorpay order creation with discount...');
    const orderId = generateOrderId();
    const customerId = generateCustomerId(testUser._id);
    
    const orderData = {
      orderId,
      amount: formattedAmount,
      currency: 'INR',
      customerId,
      customerPhone: '9999999999',
      customerEmail: testUser.email,
      customerName: testUser.displayName,
      orderMeta: {
        project_id: testProject._id,
        project_title: testProject.title,
        user_id: testUser._id,
        has_discount: true,
        discount_code: testDiscountCode.code,
        original_price: originalPrice,
        discount_amount: discountAmount,
        final_price: finalPrice
      }
    };

    console.log('   Creating order with data:', {
      orderId: orderData.orderId,
      amount: orderData.amount,
      currency: orderData.currency,
      hasDiscount: true,
      discountCode: testDiscountCode.code,
      originalPrice: originalPrice,
      finalPrice: finalPrice
    });

    const razorpayOrder = await createPaymentOrder(orderData);
    console.log('   ✅ Razorpay order created successfully:', razorpayOrder.id);
    console.log(`   Order Amount: ${razorpayOrder.amount} paise (₹${razorpayOrder.amount / 100})`);
    console.log(`   Order Status: ${razorpayOrder.status}\n`);

    // 5. Test order status retrieval
    console.log('5. Testing order status retrieval...');
    const orderStatus = await getOrderStatus(razorpayOrder.id);
    console.log('   ✅ Order status retrieved successfully:', orderStatus.status);
    console.log(`   Order ID: ${orderStatus.id}`);
    console.log(`   Amount: ${orderStatus.amount} paise\n`);

    // 6. Test payment record structure (mock)
    console.log('6. Testing payment record structure...');
    const mockPaymentRecord = {
      orderId: orderData.orderId,
      razorpayOrderId: razorpayOrder.id,
      user: testUser._id,
      project: testProject._id,
      amount: formattedAmount,
      currency: 'INR',
      status: 'ACTIVE',
      discountCode: {
        code: testDiscountCode.code,
        discountAmount: discountAmount,
        originalPrice: originalPrice,
        finalPrice: finalPrice
      },
      metadata: {
        hasDiscount: true,
        source: 'test'
      }
    };

    console.log('   Payment Record Structure:');
    console.log('   - Order ID:', mockPaymentRecord.orderId);
    console.log('   - Razorpay Order ID:', mockPaymentRecord.razorpayOrderId);
    console.log('   - Amount:', mockPaymentRecord.amount);
    console.log('   - Discount Code:', mockPaymentRecord.discountCode.code);
    console.log('   - Original Price:', mockPaymentRecord.discountCode.originalPrice);
    console.log('   - Final Price:', mockPaymentRecord.discountCode.finalPrice);
    console.log('   - Discount Amount:', mockPaymentRecord.discountCode.discountAmount);
    console.log('   Result: ✅ Payment record structure correct\n');

    // 7. Test commission calculation with discount
    console.log('7. Testing commission calculation with discounted amount...');
    const platformCommissionRate = 0.15; // 15%
    const sellerCommissionRate = 0.85; // 85%
    
    const totalAmountInPaise = Math.round(finalPrice * 100);
    const platformCommissionInPaise = Math.round(totalAmountInPaise * platformCommissionRate);
    const sellerCommissionInPaise = Math.round(totalAmountInPaise * sellerCommissionRate);
    
    console.log(`   Total Amount: ₹${finalPrice} (${totalAmountInPaise} paise)`);
    console.log(`   Platform Commission (15%): ₹${(platformCommissionInPaise / 100).toFixed(2)}`);
    console.log(`   Seller Commission (85%): ₹${(sellerCommissionInPaise / 100).toFixed(2)}`);
    console.log('   Result: ✅ Commission calculation with discount correct\n');

    console.log('🎉 All discount payment flow tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Razorpay integration working');
    console.log('✅ Discount calculation correct');
    console.log('✅ Payment amount formatting valid');
    console.log('✅ Order creation with discount successful');
    console.log('✅ Order status retrieval working');
    console.log('✅ Payment record structure correct');
    console.log('✅ Commission calculation with discount accurate');
    console.log('\n🔧 Integration Status: READY FOR PRODUCTION');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testDiscountPaymentFlow();
